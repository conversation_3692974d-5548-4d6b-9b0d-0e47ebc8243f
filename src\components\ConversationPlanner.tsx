
import React, { Suspense, lazy } from "react";
import { Api<PERSON>eyManager } from "./conversation-planner/ApiKeyManager";
import { QuestionAnalyzer } from "./conversation-planner/QuestionAnalyzer";
import { PlannerHeader } from "./conversation-planner/PlannerHeader";
import { useAnalysisResults } from "@/hooks/useAnalysisResults";
import { useUIStore } from "@/stores/useUIStore";
import { ComponentLoader } from "@/components/ui/loading-fallback";

// Lazy load the heavy AnalysisResults component
const AnalysisResults = lazy(() => 
  import("./conversation-planner/AnalysisResults").then(module => ({ default: module.AnalysisResults }))
);

export const ConversationPlanner: React.FC = () => {
  const { handleAnalyze } = useAnalysisResults();
  const chatbotMode = useUIStore((state) => state.chatbotMode);

  return (
    <div id="conversation-planner-container" className="max-w-7xl mx-auto space-y-8">
      <PlannerHeader />      {!chatbotMode && (
        <div className="grid grid-cols-1 xl:grid-cols-12 gap-8">
          {/* Left Column - API Key and Question Analyzer */}
          <div className="xl:col-span-12 space-y-6">
            <div id="api-key-manager-wrapper" className="order-1">
              <ApiKeyManager />
            </div>
            <div className="order-2">
              <QuestionAnalyzer onAnalyze={handleAnalyze} />
            </div>
          </div>
        </div>
      )}
      
      {/* Analysis Results - Full Width */}
      <div className="mt-8">
        <Suspense fallback={<ComponentLoader message="Loading analysis results..." />}>
          <AnalysisResults />
        </Suspense>
      </div>
    </div>
  );
};
