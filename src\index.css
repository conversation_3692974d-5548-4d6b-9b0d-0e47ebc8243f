/* Import enterprise styles */
@import './styles/enterprise-historical-analysis.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71% 4%; /* Near black for strong contrast */
    
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    --primary: 221 83% 53%; /* Professional Blue */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 221 83% 48%;

    --secondary: 220 14% 96%; /* Lighter gray */
    --secondary-foreground: 224 71% 4%;

    --muted: 220 14% 96%;
    --muted-foreground: 215 20% 65%;

    --accent: 220 14% 96%;
    --accent-foreground: 224 71% 4%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 85%;
    --ring: 221 83% 53%;

    --radius: 0.5rem;

    /* Semantic Colors */
    --success: 142 71% 45%;
    --success-foreground: 0 0% 100%;
    --warning: 48 95% 45%;
    --warning-foreground: 0 0% 100%;
    
    /* Analysis Type Colors */
    --analysis-multiple-bg: 259 92% 95%;
    --analysis-multiple-fg: 259 84% 60%;
    --analysis-deep-bg: 142 76% 95%;
    --analysis-deep-fg: 142 71% 45%;
    --analysis-character-bg: 48 96% 95%;
    --analysis-character-fg: 48 95% 45%;
  }

  .dark {
    --background: 220 13% 11%; /* Dark Slate */
    --foreground: 210 40% 98%;
    --card: 220 13% 15%; /* Slightly lighter slate */
    --card-foreground: 210 40% 98%;
    --popover: 220 13% 11%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 60%; /* Brighter blue for dark mode */
    --primary-foreground: 224 71% 4%;
    --primary-hover: 217 91% 65%;
    --secondary: 220 13% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 220 13% 20%;
    --muted-foreground: 215 20% 65%;
    --accent: 220 13% 20%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 25%;
    --input: 220 13% 25%;
    --ring: 217 91% 60%;
    --surface-hover: 220 13% 22%;

    /* Semantic Colors */
    --success: 142 71% 55%;
    --success-foreground: 224 71% 4%;
    --warning: 48 95% 55%;
    --warning-foreground: 224 71% 4%;
    
    /* Analysis Type Colors */
    --analysis-multiple-bg: 259 92% 25%;
    --analysis-multiple-fg: 259 92% 85%;
    --analysis-deep-bg: 142 76% 20%;
    --analysis-deep-fg: 142 71% 75%;
    --analysis-character-bg: 48 96% 20%;
    --analysis-character-fg: 48 95% 75%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1; /* Enable ligatures for better typography */
  }

  blockquote {
    background-color: hsl(var(--muted));
    border-left-width: 4px;
    border-left-style: solid;
  }

  blockquote p {
    font-size: 1rem;
    color: hsl(var(--muted-foreground));
  }

  em, i {
    font-style: italic;
    color: hsl(var(--muted-foreground) / 0.8);
  }
  blockquote p {
    font-size: 1rem;
    color: hsl(var(--muted-foreground));
  }

  em, i {
    font-style: italic;
    color: hsl(var(--muted-foreground) / 0.8);
  }
}

/* Layout Enhancement Styles */
@layer utilities {
  /* Smooth scrolling and transitions */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus styles */
  .focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  /* Enhanced shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .dark .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.2), 0 10px 20px -2px rgba(0, 0, 0, 0.1);
  }

  /* Animated gradients */
  .gradient-animation {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  /* Container spacing */
  .container-spacing {
    padding-left: max(1rem, calc((100vw - 1280px) / 2));
    padding-right: max(1rem, calc((100vw - 1280px) / 2));
  }

  /* Card hover effects */
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .dark .card-hover:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  }

  /* Button enhancement */
  .btn-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-hover)));
    transition: all 0.3s ease;
  }

  .btn-gradient:hover {
    background: linear-gradient(135deg, hsl(var(--primary-hover)), hsl(var(--primary)));
    transform: translateY(-1px);
  }

  /* Loading states */
  .loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .loading-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    background-size: 200% 100%;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  /* Mobile-specific adjustments */
  .mobile-full {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
}

/* Enhanced responsive tab design */
@media (max-width: 640px) {
  .tab-responsive {
    max-width: 100%;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
  }
  
  .tab-text-small {
    font-size: 0.8rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .tab-responsive {
    max-width: 32rem;
    font-size: 0.9rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
