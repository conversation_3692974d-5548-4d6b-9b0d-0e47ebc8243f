import { ChatOpenAI } from '@langchain/openai';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { AnalysisResult } from '@/types/conversation';
import { AnalysisCluster, ChatSimulation } from '@/components/visual-analysis/types';

/**
 * LangChain integration service for the Living Data Canvas
 */
export class LangChainIntegration {
  private llm: ChatOpenAI;
  private outputParser: StringOutputParser;

  constructor(apiKey?: string, model: string = 'gpt-4') {
    this.llm = new ChatOpenAI({
      openAIApiKey: apiKey || process.env.OPENAI_API_KEY,
      modelName: model,
      temperature: 0.7,
    });
    this.outputParser = new StringOutputParser();
  }

  /**
   * Generate analysis insights from chat analysis records
   */
  async generateAnalysisInsights(analysisResults: AnalysisResult[]): Promise<string> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are an AI assistant analyzing chat analysis records. 
      
      Given the following analysis records:
      {analysisData}
      
      Please provide:
      1. Key themes and patterns across the analyses
      2. Insights about the types of questions being asked
      3. Recommendations for improving analysis quality
      4. Potential areas for further exploration
      
      Format your response in a clear, structured manner.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const analysisData = analysisResults.map(result => ({
      question: result.question,
      analysis: result.analysis,
      type: result.analysisType,
      rating: result.rating
    }));

    return await chain.invoke({
      analysisData: JSON.stringify(analysisData, null, 2)
    });
  }

  /**
   * Generate cluster descriptions and insights
   */
  async generateClusterInsights(cluster: AnalysisCluster, analysisResults: AnalysisResult[]): Promise<string> {
    const clusterAnalyses = analysisResults.filter(result => 
      cluster.nodeIds.includes(result.id)
    );

    const prompt = ChatPromptTemplate.fromTemplate(`
      You are analyzing a cluster of related chat analysis records.
      
      Cluster Name: {clusterName}
      Cluster Description: {clusterDescription}
      
      Analysis Records in this cluster:
      {clusterData}
      
      Please provide:
      1. A comprehensive summary of what this cluster represents
      2. Common themes and patterns within the cluster
      3. Key insights and findings
      4. Suggested next steps or follow-up questions
      5. How this cluster relates to broader research or business objectives
      
      Be specific and actionable in your recommendations.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    return await chain.invoke({
      clusterName: cluster.name,
      clusterDescription: cluster.description || 'No description provided',
      clusterData: JSON.stringify(clusterAnalyses, null, 2)
    });
  }

  /**
   * Generate simulation prompts based on analysis records
   */
  async generateSimulationPrompts(sourceAnalyses: AnalysisResult[], simulationType: string = 'exploration'): Promise<string[]> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are creating simulation prompts for testing and exploring AI analysis capabilities.
      
      Based on these source analysis records:
      {sourceData}
      
      Simulation Type: {simulationType}
      
      Generate 5 diverse and thought-provoking prompts that would:
      1. Build upon the existing analyses
      2. Explore different angles or perspectives
      3. Test edge cases or challenging scenarios
      4. Encourage deeper investigation
      5. Validate or challenge existing findings
      
      Return the prompts as a JSON array of strings.
      Each prompt should be clear, specific, and designed to generate valuable insights.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const response = await chain.invoke({
      sourceData: JSON.stringify(sourceAnalyses, null, 2),
      simulationType
    });

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback: split by lines and clean up
      return response
        .split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').trim())
        .slice(0, 5);
    }
  }

  /**
   * Analyze connections between analysis records
   */
  async analyzeConnections(sourceAnalysis: AnalysisResult, targetAnalysis: AnalysisResult): Promise<{
    strength: number;
    relationship: string;
    insights: string;
  }> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are analyzing the relationship between two chat analysis records.
      
      Source Analysis:
      Question: {sourceQuestion}
      Analysis: {sourceAnalysis}
      Type: {sourceType}
      
      Target Analysis:
      Question: {targetQuestion}
      Analysis: {targetAnalysis}
      Type: {targetType}
      
      Please analyze the relationship and provide:
      1. Connection strength (0.0 to 1.0, where 1.0 is highly related)
      2. Type of relationship (e.g., "complementary", "contradictory", "sequential", "thematic")
      3. Detailed insights about how these analyses relate to each other
      
      Return your response as JSON in this format:
      {{
        "strength": 0.0,
        "relationship": "relationship_type",
        "insights": "detailed explanation"
      }}
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const response = await chain.invoke({
      sourceQuestion: sourceAnalysis.question,
      sourceAnalysis: sourceAnalysis.analysis,
      sourceType: sourceAnalysis.analysisType,
      targetQuestion: targetAnalysis.question,
      targetAnalysis: targetAnalysis.analysis,
      targetType: targetAnalysis.analysisType,
    });

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback response
      return {
        strength: 0.5,
        relationship: 'thematic',
        insights: 'Unable to parse detailed relationship analysis.'
      };
    }
  }

  /**
   * Generate follow-up questions for analysis records
   */
  async generateFollowUpQuestions(analysisResult: AnalysisResult): Promise<string[]> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are generating follow-up questions for a chat analysis record.
      
      Original Question: {question}
      Analysis: {analysis}
      Analysis Type: {analysisType}
      
      Generate 3-5 thoughtful follow-up questions that would:
      1. Deepen the understanding of the topic
      2. Explore different perspectives or angles
      3. Address potential gaps or limitations
      4. Connect to related concepts or applications
      5. Challenge assumptions or explore edge cases
      
      Return the questions as a JSON array of strings.
      Each question should be specific, actionable, and likely to generate valuable insights.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const response = await chain.invoke({
      question: analysisResult.question,
      analysis: analysisResult.analysis,
      analysisType: analysisResult.analysisType,
    });

    try {
      return JSON.parse(response);
    } catch (error) {
      // Fallback: use existing follow-up questions or generate simple ones
      return analysisResult.followUpQuestions || [
        `What are the practical applications of the insights from: "${analysisResult.question}"?`,
        `What potential challenges or limitations should be considered?`,
        `How does this relate to current industry trends or best practices?`
      ];
    }
  }

  /**
   * Summarize simulation results
   */
  async summarizeSimulationResults(simulation: ChatSimulation): Promise<string> {
    if (!simulation.results || simulation.results.length === 0) {
      return 'No results available for this simulation.';
    }

    const prompt = ChatPromptTemplate.fromTemplate(`
      You are summarizing the results of a chat simulation.
      
      Simulation: {simulationName}
      Description: {simulationDescription}
      
      Prompts and Results:
      {resultsData}
      
      Please provide:
      1. Executive summary of key findings
      2. Patterns and themes across the results
      3. Most interesting or surprising insights
      4. Recommendations based on the findings
      5. Suggestions for future simulations or research
      
      Format your response in a clear, structured manner suitable for stakeholders.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const resultsData = simulation.results.map(result => ({
      prompt: simulation.prompts.find(p => p.id === result.promptId)?.content || 'Unknown prompt',
      response: result.response,
      timestamp: result.timestamp
    }));

    return await chain.invoke({
      simulationName: simulation.name,
      simulationDescription: simulation.description,
      resultsData: JSON.stringify(resultsData, null, 2)
    });
  }

  /**
   * Generate canvas insights and recommendations
   */
  async generateCanvasInsights(
    analysisResults: AnalysisResult[],
    clusters: AnalysisCluster[],
    simulations: ChatSimulation[]
  ): Promise<string> {
    const prompt = ChatPromptTemplate.fromTemplate(`
      You are analyzing a comprehensive Living Data Canvas with chat analysis records, clusters, and simulations.
      
      Analysis Records: {analysisCount} total
      Clusters: {clusterCount} total
      Simulations: {simulationCount} total
      
      Sample Data:
      {sampleData}
      
      Please provide:
      1. Overall assessment of the research landscape
      2. Key themes and patterns across all data
      3. Gaps or opportunities for further exploration
      4. Recommendations for optimizing the research process
      5. Strategic insights for decision-making
      
      Focus on actionable insights that can guide future research and analysis efforts.
    `);

    const chain = RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);

    const sampleData = {
      recentAnalyses: analysisResults.slice(0, 3).map(a => ({
        question: a.question,
        type: a.analysisType,
        rating: a.rating
      })),
      clusterSummary: clusters.map(c => ({
        name: c.name,
        nodeCount: c.nodeIds.length,
        tags: c.tags
      })),
      simulationSummary: simulations.map(s => ({
        name: s.name,
        status: s.status,
        resultCount: s.results?.length || 0
      }))
    };

    return await chain.invoke({
      analysisCount: analysisResults.length,
      clusterCount: clusters.length,
      simulationCount: simulations.length,
      sampleData: JSON.stringify(sampleData, null, 2)
    });
  }

  /**
   * Create a custom chain for specific analysis tasks
   */
  createCustomChain(promptTemplate: string) {
    const prompt = ChatPromptTemplate.fromTemplate(promptTemplate);
    return RunnableSequence.from([
      prompt,
      this.llm,
      this.outputParser,
    ]);
  }

  /**
   * Update model configuration
   */
  updateModel(model: string, temperature: number = 0.7) {
    this.llm = new ChatOpenAI({
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: model,
      temperature,
    });
  }
}
