import React from "react";
import { Badge } from "@/components/ui/badge";
import { Clock, Sparkles } from "lucide-react";
import { AnalysisResult, ConversationStyle } from "@/types/conversation";
import { ResultCard } from "./ResultCard";
import { ChatbotModeManager } from "./ChatbotModeManager";
import { NoteManager } from "./NoteManager";
import { RefinementManager } from "./RefinementManager";
import { AnalysisVisualizationWrapper } from "./visualization/AnalysisVisualizationWrapper";
import { useAnalysisStore } from "@/stores/useAnalysisStore";
import { useSettingsStore } from "@/stores/useSettingsStore";
import { useResultsManagement } from "@/hooks/useResultsManagement";
import { useToast } from "@/hooks/use-toast";

interface AnalysisResultsProps {
  results?: AnalysisResult[];
  onSave?: (result: AnalysisResult) => void;
  onDelete?: (id: string) => void;
  onUpdateResult?: (id: string, updatedResult: AnalysisResult) => void;
  onRefine?: (id: string, prompt: string) => Promise<void>;
  onFollowUpQuestion?: (question: string) => void;
  onAddNote?: (questionId: string, answerId: string, answerContent: string) => void;
  onEnterChatMode?: (result: AnalysisResult, focusedAnswer?: string) => void;
  showAddNote?: boolean;
  apiKey?: string;
  selectedModel?: string;
  style?: ConversationStyle;
}

export const AnalysisResults: React.FC<AnalysisResultsProps> = (props) => {
  const { results: propsResults, onEnterChatMode, showAddNote = true } = props;

  const analysisStore = useAnalysisStore();
  const settingsStore = useSettingsStore();
  const resultsManagement = useResultsManagement();
  const { toast } = useToast();

  const results = propsResults ?? analysisStore.analysisResults;

  if (results.length === 0) {
    return null;
  }

  const onSave = props.onSave ?? resultsManagement.handleSaveAnalysis;
  const onDelete = props.onDelete ?? resultsManagement.handleDeleteResult;
  const onUpdateResult = props.onUpdateResult ?? analysisStore.updateResult;
  const onFollowUpQuestion = props.onFollowUpQuestion ?? analysisStore.setQuestion;

  const apiKey = props.apiKey ?? settingsStore.settings.openRouterApiKey;
  const selectedModel = props.selectedModel ?? settingsStore.settings.selectedModel;
  const style = props.style ?? settingsStore.settings.style;

  const handleRate = async (id: string, rating: number) => {
    const result = results.find((r) => r.id === id);
    if (result) {
      const updatedResult = { ...result, rating };
      try {
        await onUpdateResult(id, updatedResult);
        toast({
          title: "Rating saved",
          description: `You rated this analysis ${rating} star${rating !== 1 ? 's' : ''}.`,
        });
      } catch (error) {
        toast({
          title: "Rating failed",
          description: "Could not save your rating, rolling back.",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <ChatbotModeManager apiKey={apiKey} selectedModel={selectedModel} onSaveAnalysis={onSave}>
      {({ handleEnterChatMode }) => (
        <NoteManager results={results}>
          {({ handleAddNote }) => (
            <RefinementManager
              apiKey={apiKey}
              selectedModel={selectedModel}
              style={style}
              results={results}
              onUpdateResult={onUpdateResult}
            >
              {({ handleRefine, isRefining }) => (
                <div className="space-y-6 mb-8">
                  <div className="flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                    <h3 className="text-2xl font-bold flex items-center text-slate-800">
                      <Sparkles className="mr-3 h-6 w-6 text-blue-600" />
                      Analysis Results
                    </h3>
                    <Badge variant="secondary" className="flex items-center bg-blue-100 text-blue-800 border-blue-200 px-3 py-1">
                      <Clock className="mr-2 h-4 w-4" />
                      {results.length} result{results.length !== 1 ? "s" : ""}
                    </Badge>
                  </div>

                  {/* Enhanced Visualization Wrapper */}
                  <AnalysisVisualizationWrapper
                    results={results}
                    onFollowUpQuestion={onFollowUpQuestion}
                    onResultSelect={(result) => {
                      // Optional: Add any additional logic when a result is selected
                      console.log('Selected result:', result.id);
                    }}
                  >
                    {/* Traditional card view content */}
                    <div className="space-y-4">
                      {results.map((result) => (
                        <ResultCard
                          key={result.id}
                          result={result}
                          onSave={onSave}
                          onDelete={onDelete}
                          onRate={handleRate}
                          onRefine={props.onRefine ?? handleRefine}
                          onFollowUpQuestion={onFollowUpQuestion}
                          onAnswerSelect={(answer) => onFollowUpQuestion(answer)}
                          onAddNote={showAddNote ? props.onAddNote ?? handleAddNote : undefined}
                          onEnterChatMode={onEnterChatMode || handleEnterChatMode}
                          isRefining={isRefining(result.id)}
                        />
                      ))}
                    </div>
                  </AnalysisVisualizationWrapper>
                </div>
              )}
            </RefinementManager>
          )}
        </NoteManager>
      )}
    </ChatbotModeManager>
  );
};
